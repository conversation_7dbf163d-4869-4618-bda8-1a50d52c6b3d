import { NextRequest, NextResponse } from 'next/server';
import { getPropertyDetails, getPropertyDetailsByAddress } from '@/app/lib/realestateapi';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Property details request:', body);

    let propertyData = null;

    // Try to get property details by ID first, then by address
    if (body.propertyId) {
      propertyData = await getPropertyDetails(body.propertyId);
    } else if (body.address) {
      propertyData = await getPropertyDetailsByAddress(body.address);
    } else {
      return NextResponse.json(
        { error: 'Either propertyId or address is required' },
        { status: 400 }
      );
    }

    if (!propertyData) {
      return NextResponse.json(
        { error: 'Property details not found' },
        { status: 404 }
      );
    }

    // Map the Real Estate API response to our PropertyDetails format
    const mappedPropertyDetails = {
      address: body.address || {
        street: propertyData.address || '',
        city: propertyData.city || '',
        state: propertyData.state || '',
        zipCode: propertyData.zip || '',
        fullAddress: propertyData.address || `${propertyData.address}, ${propertyData.city}, ${propertyData.state} ${propertyData.zip}`,
      },
      propertyId: propertyData.id?.toString() || propertyData.propertyId,
      apn: propertyData.lotInfo?.apn,
      county: propertyData.county,
      latitude: propertyData.latitude,
      longitude: propertyData.longitude,
      propertyType: propertyData.propertyType,
      vacant: propertyData.vacant,
      absenteeOwner: propertyData.absenteeOwner,
      ownerOccupied: propertyData.ownerOccupied,
      
      // Property characteristics
      bedrooms: propertyData.propertyInfo?.bedrooms,
      bathrooms: propertyData.propertyInfo?.bathrooms,
      squareFeet: propertyData.propertyInfo?.squareFeet,
      lotSize: propertyData.propertyInfo?.lotSquareFeet,
      lotSquareFeet: propertyData.propertyInfo?.lotSquareFeet,
      acres: propertyData.lotInfo?.acres,
      yearBuilt: propertyData.propertyInfo?.yearBuilt,
      stories: propertyData.propertyInfo?.stories,
      rooms: propertyData.propertyInfo?.rooms,
      units: propertyData.propertyInfo?.units,
      fireplace: propertyData.propertyInfo?.fireplace,
      pool: propertyData.propertyInfo?.pool,
      garageType: propertyData.propertyInfo?.garageType,
      heatingType: propertyData.propertyInfo?.heatingType,
      airConditioningType: propertyData.propertyInfo?.airConditioningType,
      roofMaterial: propertyData.propertyInfo?.roofMaterial,
      exteriorWalls: propertyData.propertyInfo?.exteriorWalls,
      
      // Legal and lot information
      legalDescription: propertyData.lotInfo?.legalDescription,
      subdivision: propertyData.lotInfo?.subdivision,
      zoning: propertyData.lotInfo?.zoning,
      landUse: propertyData.lotInfo?.landUse,
      
      // Owner information
      ownerName: propertyData.ownerInfo ? 
        `${propertyData.ownerInfo.owner1FirstName || ''} ${propertyData.ownerInfo.owner1LastName || ''}`.trim() : undefined,
      ownerType: propertyData.ownerInfo?.ownerType,
      yearsOwned: propertyData.ownerInfo?.yearsOwned,
      
      // Financial information
      estimatedValue: propertyData.taxInfo?.marketValue || propertyData.estimatedValue,
      assessedValue: propertyData.taxInfo?.assessedValue,
      taxAmount: propertyData.taxInfo?.taxAmount,
    };

    console.log('Mapped property details:', mappedPropertyDetails);

    return NextResponse.json({
      success: true,
      propertyDetails: mappedPropertyDetails,
      rawData: propertyData // Include raw data for debugging
    });

  } catch (error) {
    console.error('Property details API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch property details', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
