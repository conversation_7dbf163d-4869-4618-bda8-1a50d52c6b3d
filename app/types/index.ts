export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  fullAddress: string;
}

// Property type enum
export type PropertyType = 
  | 'SINGLE_FAMILY' 
  | 'CONDO' 
  | 'TOWNHOUSE' 
  | 'MULTI_FAMILY' 
  | 'LAND' 
  | 'COMMERCIAL';

export interface PropertyDetails {
  address: Address;
  listingPrice?: number;
  legalDescription?: string;
  propertyType?: PropertyType;
  yearBuilt?: number;
  bedrooms?: number;
  bathrooms?: number;
  squareFeet?: number;
  lotSize?: number;
  mls?: string;
}

export interface BuyerInfo {
  name: string;
  email: string;
  phone?: string;
  address?: Address;
}

export interface OfferDetails {
  offerPrice: number;
  titleCompany?: string;
  earnestDeposit: number;
  earnestDepositDays: number;
  inspectionDays: number;
  offerExpirationDate: string;
  paymentType: 'CASH' | 'CONVENTIONAL' | 'FHA' | 'VA' | 'DSCR';
  closingDate?: string;
  additionalTerms?: string;
}

export interface FormData {
  step: number;
  propertyDetails?: PropertyDetails;
  buyerInfo?: BuyerInfo;
  offerDetails?: OfferDetails;
  agreementType?: 'PARTIAL_FREE' | 'PARTIAL_PAID' | 'OFFICIAL';
  requiredForms?: string[];
  email?: string;
  devMode?: boolean;
}

export interface AutoCompleteResult {
  id: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  fullAddress: string;
}

export interface VerifiedAddress {
  verified: boolean;
  confidence: number;
  address: Address;
  propertyId?: string;
}

export interface RequiredForm {
  id: string;
  name: string;
  description: string;
  required: boolean;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  officialAgreements: number;
  unofficialAgreements: number;
  features: string[];
}
