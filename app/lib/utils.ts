import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
}

export function formatDate(date: string | Date): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(d);
}

export function extractAddressFromUrl(url: string): string | null {
  // Extract address from Zillow or other real estate listing URLs
  try {
    const urlObj = new URL(url);
    
    // Zillow URL pattern
    if (urlObj.hostname.includes('zillow.com')) {
      const pathParts = urlObj.pathname.split('/');
      const addressPart = pathParts.find(part => part.includes('_'));
      if (addressPart) {
        return addressPart.replace(/_/g, ' ').replace(/-/g, ' ');
      }
    }
    
    // Add other real estate sites as needed
    
    return null;
  } catch {
    return null;
  }
}

export function generateDraftId(): string {
  return `draft_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}