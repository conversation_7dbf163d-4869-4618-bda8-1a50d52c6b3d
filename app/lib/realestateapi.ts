import axios from 'axios';
import { AutoCompleteResult, VerifiedAddress } from '@/app/types';
import { extractAddressFromUrl } from './utils';

const API_BASE_URL = process.env.REAL_ESTATE_API_URL || 'https://api.realestateapi.com/v2';
const API_KEY = process.env.REAL_ESTATE_API_KEY || 'AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914';

// Create axios instance with proper configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-api-key': API_KEY, // Real Estate API uses x-api-key header (lowercase)
  },
  timeout: 10000, // 10 second timeout
});

export async function searchAddress(query: string): Promise<AutoCompleteResult[]> {
  try {
    // Check if the query is a URL and extract address if so
    const extractedAddress = extractAddressFromUrl(query);
    const searchQuery = extractedAddress || query;

    // Ensure minimum length requirement
    if (searchQuery.length < 3) {
      return [];
    }

    console.log('Searching address:', searchQuery);

    const response = await apiClient.post('/AutoComplete', {
      search: searchQuery,
      search_types: ['A'], // A for full address autocomplete
    });

    console.log('AutoComplete API response:', response.data);

    // Handle different response structures
    if (response.data) {
      let results = [];

      // Check if results are in response.data.data (Real Estate API format)
      if (response.data.data && Array.isArray(response.data.data)) {
        results = response.data.data;
      } else if (response.data.results && Array.isArray(response.data.results)) {
        results = response.data.results;
      } else if (Array.isArray(response.data)) {
        results = response.data;
      }

      return results.map((result: any, index: number) => {
        // Construct the complete street address with house number
        const houseNumber = result.house || '';
        const streetName = result.street?.trim() || '';
        const unit = result.unit ? `, # ${result.unit}` : '';

        // Create the complete address with house number + street + unit
        let completeStreetAddress = '';
        if (houseNumber && streetName) {
          completeStreetAddress = `${houseNumber} ${streetName}`.trim() + unit;
        } else if (streetName) {
          completeStreetAddress = streetName.trim() + unit;
        } else {
          completeStreetAddress = result.address || '';
        }

        console.log('Mapping result:', {
          house: result.house,
          street: result.street,
          unit: result.unit,
          completeStreetAddress,
          originalAddress: result.address
        });

        return {
          id: result.id || result.property_id || `autocomplete-${index}`,
          address: completeStreetAddress,
          city: result.city || '',
          state: result.state || '',
          zipCode: result.zip || result.zipCode || '',
          fullAddress: result.address || result.title ||
                      (completeStreetAddress +
                      (result.city ? `, ${result.city}` : '') +
                      (result.state ? `, ${result.state}` : '') +
                      (result.zip ? ` ${result.zip}` : '')),
        };
      }).filter((result: any) => result.address && result.city && result.state);
    }

    return [];
  } catch (error: any) {
    console.error('Error searching address:', error);

    // Log more detailed error information
    if (error.response) {
      console.error('API Error Response:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
      });
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Request setup error:', error.message);
    }

    return [];
  }
}

export async function verifyAddress(address: {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}): Promise<VerifiedAddress | null> {
  try {
    console.log('Verifying address:', address);

    const response = await apiClient.post('/AddressVerification', {
      address: {
        street: address.street,
        city: address.city,
        state: address.state,
        zip: address.zipCode,
      },
    });

    console.log('Address verification response:', response.data);

    if (response.data) {
      // Handle different response structures
      const isVerified = response.data.verified === true || response.data.status === 'verified';
      const results = response.data.results || [response.data];
      const result = results[0] || response.data;

      if (isVerified && result) {
        return {
          verified: true,
          confidence: result.confidence || result.score || 0,
          address: {
            street: result.street || result.address || address.street,
            city: result.city || address.city,
            state: result.state || address.state,
            zipCode: result.zip || result.zipCode || address.zipCode,
            fullAddress: result.full_address ||
                        `${result.street || result.address || address.street}, ${result.city || address.city}, ${result.state || address.state} ${result.zip || result.zipCode || address.zipCode}`,
          },
          propertyId: result.property_id || result.id,
        };
      }
    }

    // Return unverified address if verification fails
    return {
      verified: false,
      confidence: 0,
      address: {
        ...address,
        fullAddress: `${address.street}, ${address.city}, ${address.state} ${address.zipCode}`,
      },
    };
  } catch (error: any) {
    console.error('Error verifying address:', error);

    // Log detailed error information
    if (error.response) {
      console.error('Address Verification API Error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    }

    // Return unverified address on error
    return {
      verified: false,
      confidence: 0,
      address: {
        ...address,
        fullAddress: `${address.street}, ${address.city}, ${address.state} ${address.zipCode}`,
      },
    };
  }
}

export async function getPropertyDetails(propertyId: string): Promise<any> {
  try {
    console.log('Getting property details for:', propertyId);
    const response = await apiClient.get(`/Property/${propertyId}`);
    console.log('Property details response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error getting property details:', error);
    if (error.response) {
      console.error('Property Details API Error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
      });
    }
    return null;
  }
}

// Test function to check API connectivity
export async function testApiConnection(): Promise<{ success: boolean; message: string }> {
  try {
    console.log('Testing API connection...');
    console.log('API Base URL:', API_BASE_URL);
    console.log('API Key configured:', !!API_KEY);

    // Test with a simple autocomplete request
    const response = await apiClient.post('/AutoComplete', {
      search: 'test',
      search_types: ['A'],
    });

    console.log('API test response received:', !!response.data);

    return {
      success: true,
      message: 'API connection successful',
    };
  } catch (error: any) {
    console.error('API connection test failed:', error);

    let message = 'API connection failed';
    if (error.response) {
      message += ` - Status: ${error.response.status}`;
      if (error.response.status === 401) {
        message += ' (Unauthorized - check API key)';
      } else if (error.response.status === 403) {
        message += ' (Forbidden - check API permissions)';
      }
    } else if (error.request) {
      message += ' - No response received (check network/URL)';
    } else {
      message += ` - ${error.message}`;
    }

    return {
      success: false,
      message,
    };
  }
}