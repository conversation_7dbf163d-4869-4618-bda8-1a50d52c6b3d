import axios from 'axios';
import { AutoCompleteResult, VerifiedAddress } from '@/app/types';

const API_BASE_URL = process.env.REAL_ESTATE_API_URL || 'https://api.realestateapi.com/v2';
const API_KEY = process.env.REAL_ESTATE_API_KEY || '';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${API_KEY}`,
  },
});

export async function searchAddress(query: string): Promise<AutoCompleteResult[]> {
  try {
    const response = await apiClient.post('/AutoComplete', {
      search: query,
      search_types: ['A'], // A for full address
    });

    if (response.data && response.data.results) {
      return response.data.results.map((result: any) => ({
        id: result.id,
        address: result.street,
        city: result.city,
        state: result.state,
        zipCode: result.zip,
        fullAddress: `${result.street}, ${result.city}, ${result.state} ${result.zip}`,
      }));
    }

    return [];
  } catch (error) {
    console.error('Error searching address:', error);
    return [];
  }
}

export async function verifyAddress(address: {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}): Promise<VerifiedAddress | null> {
  try {
    const response = await apiClient.post('/AddressVerification', {
      address: {
        street: address.street,
        city: address.city,
        state: address.state,
        zip: address.zipCode,
      },
    });

    if (response.data && response.data.verified) {
      const result = response.data.results[0];
      return {
        verified: true,
        confidence: result.confidence || 0,
        address: {
          street: result.street,
          city: result.city,
          state: result.state,
          zipCode: result.zip,
          fullAddress: `${result.street}, ${result.city}, ${result.state} ${result.zip}`,
        },
        propertyId: result.property_id,
      };
    }

    return {
      verified: false,
      confidence: 0,
      address: {
        ...address,
        fullAddress: `${address.street}, ${address.city}, ${address.state} ${address.zipCode}`,
      },
    };
  } catch (error) {
    console.error('Error verifying address:', error);
    return null;
  }
}

export async function getPropertyDetails(propertyId: string): Promise<any> {
  try {
    const response = await apiClient.get(`/Property/${propertyId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting property details:', error);
    return null;
  }
}